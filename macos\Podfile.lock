PODS:
  - dynamic_color (0.0.2):
    - FlutterMacOS
  - FlutterMacOS (1.0.0)
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - rust_lib_yumcha (0.0.1):
    - FlutterMacOS
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqlite3 (3.49.2):
    - sqlite3/common (= 3.49.2)
  - sqlite3/common (3.49.2)
  - sqlite3/dbstatvtab (3.49.2):
    - sqlite3/common
  - sqlite3/fts5 (3.49.2):
    - sqlite3/common
  - sqlite3/math (3.49.2):
    - sqlite3/common
  - sqlite3/perf-threadsafe (3.49.2):
    - sqlite3/common
  - sqlite3/rtree (3.49.2):
    - sqlite3/common
  - sqlite3_flutter_libs (0.0.1):
    - Flutter
    - FlutterMacOS
    - sqlite3 (~> 3.49.2)
    - sqlite3/dbstatvtab
    - sqlite3/fts5
    - sqlite3/math
    - sqlite3/perf-threadsafe
    - sqlite3/rtree

DEPENDENCIES:
  - dynamic_color (from `Flutter/ephemeral/.symlinks/plugins/dynamic_color/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - rust_lib_yumcha (from `Flutter/ephemeral/.symlinks/plugins/rust_lib_yumcha/macos`)
  - shared_preferences_foundation (from `Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqlite3_flutter_libs (from `Flutter/ephemeral/.symlinks/plugins/sqlite3_flutter_libs/darwin`)

SPEC REPOS:
  trunk:
    - sqlite3

EXTERNAL SOURCES:
  dynamic_color:
    :path: Flutter/ephemeral/.symlinks/plugins/dynamic_color/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  rust_lib_yumcha:
    :path: Flutter/ephemeral/.symlinks/plugins/rust_lib_yumcha/macos
  shared_preferences_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin
  sqlite3_flutter_libs:
    :path: Flutter/ephemeral/.symlinks/plugins/sqlite3_flutter_libs/darwin

SPEC CHECKSUMS:
  dynamic_color: b820c000cc68df65e7ba7ff177cb98404ce56651
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  rust_lib_yumcha: 0caf61b88ae6522a39ef533a589d7f32ee0b8fcf
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sqlite3: 3c950dc86011117c307eb0b28c4a7bb449dce9f1
  sqlite3_flutter_libs: 74334e3ef2dbdb7d37e50859bb45da43935779c4

PODFILE CHECKSUM: 7eb978b976557c8c1cd717d8185ec483fd090a82

COCOAPODS: 1.16.2
