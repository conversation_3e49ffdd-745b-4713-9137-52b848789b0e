# MCP (Model Context Protocol) 集成

YumCha 现在支持 MCP (Model Context Protocol)，允许 AI 助手通过外部工具扩展其功能。

## 功能特性

- ✅ 支持多种 MCP 服务器类型（Standard I/O、HTTP、SSE）
- ✅ 图形化配置界面
- ✅ 服务器状态监控
- ✅ 工具调用集成
- ✅ 错误处理和重连机制

## 快速开始

### 1. 启用 MCP 功能

1. 打开 YumCha 应用
2. 进入 **设置** → **MCP**
3. 开启 **启用 MCP** 开关

### 2. 添加 MCP 服务器

1. 在 MCP 设置页面点击右上角的 **+** 按钮
2. 填写服务器信息：
   - **服务器名称**: 给服务器起一个描述性的名称
   - **描述**: 可选，描述服务器的功能
   - **连接类型**: 选择 Standard I/O、HTTP 或 SSE
   - **命令路径/URL**: 根据连接类型填写相应信息
   - **命令参数**: 仅对 Standard I/O 类型有效
   - **环境变量**: 仅对 Standard I/O 类型有效

### 3. 测试示例服务器

我们提供了一个简单的示例服务器，包含基本的计算和文本处理工具：

```bash
# 编译示例服务器
dart compile exe examples/simple_mcp_server.dart -o simple_mcp_server

# 在 YumCha 中配置
# 名称: 计算工具服务器
# 类型: Standard I/O
# 命令: /path/to/simple_mcp_server
```

## 支持的连接类型

### Standard I/O
- 通过标准输入输出与本地可执行文件通信
- 适用于本地工具和脚本
- 支持命令参数和环境变量配置

### HTTP
- 通过 HTTP 请求与远程服务器通信
- 适用于 Web API 和远程服务
- 支持 Streamable HTTP 协议

### Server-Sent Events (SSE)
- 通过 SSE 连接实现实时通信
- 适用于需要实时数据的场景
- 基于 HTTP 协议的流式传输

## 配置示例

### 本地文件系统工具
```
名称: 文件系统工具
类型: Standard I/O
命令: /usr/local/bin/filesystem-mcp
参数: --root /home/<USER>/documents
环境变量:
LOG_LEVEL=info
MAX_FILE_SIZE=10MB
```

### Web API 工具
```
名称: Web 搜索工具
类型: HTTP
URL: http://localhost:3000/mcp
描述: 提供网络搜索和 API 调用功能
```

### 实时数据工具
```
名称: 实时数据工具
类型: SSE
URL: http://localhost:3001/sse
描述: 提供实时数据流处理功能
```

## 开发自定义 MCP 服务器

### 使用 mcp_dart 库

```dart
import 'package:mcp_dart/mcp_dart.dart';

void main() async {
  final server = McpServer(
    Implementation(name: "my-server", version: "1.0.0"),
    options: ServerOptions(
      capabilities: ServerCapabilities(
        tools: ServerCapabilitiesTools(),
      ),
    ),
  );

  // 注册工具
  server.tool(
    "my_tool",
    description: '我的自定义工具',
    inputSchemaProperties: {
      'input': {
        'type': 'string',
        'description': '输入参数',
      },
    },
    callback: ({args, extra}) async {
      final input = args!['input'] as String;
      
      return CallToolResult(
        content: [
          TextContent(text: '处理结果: $input'),
        ],
      );
    },
  );

  // 连接到标准输入输出
  await server.connect(StdioServerTransport());
}
```

### 编译和部署

```bash
# 编译为可执行文件
dart compile exe my_server.dart -o my_server

# 在 YumCha 中配置
# 名称: 我的自定义服务器
# 类型: Standard I/O
# 命令: /path/to/my_server
```

## 故障排除

### 服务器连接失败
1. 检查命令路径是否正确
2. 确认可执行文件有执行权限
3. 查看错误信息了解具体问题

### 工具调用失败
1. 检查工具参数是否正确
2. 确认服务器是否正常运行
3. 查看服务器日志了解错误原因

### 性能问题
1. 避免在工具中执行耗时操作
2. 考虑使用异步处理
3. 限制并发工具调用数量

## 最佳实践

1. **工具设计**
   - 保持工具功能单一且明确
   - 提供清晰的参数描述
   - 处理错误情况并返回有意义的错误信息

2. **安全考虑**
   - 验证输入参数
   - 限制文件系统访问权限
   - 避免执行危险的系统命令

3. **性能优化**
   - 缓存频繁使用的数据
   - 使用连接池管理资源
   - 实现超时机制

## 相关资源

- [MCP 官方文档](https://modelcontextprotocol.io/)
- [mcp_dart 库文档](https://pub.dev/packages/mcp_dart)
- [MCP 规范](https://spec.modelcontextprotocol.io/)

## 技术架构

### 核心组件

1. **McpService**: MCP 服务管理器
   - 管理服务器连接
   - 处理工具调用
   - 监控服务器状态

2. **McpServerConfig**: 服务器配置模型
   - 存储服务器连接信息
   - 支持多种连接类型
   - 持久化配置数据

3. **McpSettingsScreen**: 配置界面
   - 图形化配置管理
   - 服务器状态显示
   - 实时连接监控

### 集成流程

1. 用户在设置界面配置 MCP 服务器
2. McpService 根据配置初始化服务器连接
3. AI 服务检测可用的 MCP 工具
4. 在聊天过程中调用相应的工具
5. 将工具结果集成到 AI 响应中

这个集成为 YumCha 提供了强大的扩展能力，让 AI 助手能够通过外部工具访问实时数据、执行复杂操作，大大增强了应用的实用性。
